import React, { useState, useEffect, useRef } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Menu } from 'primereact/menu';
import { Card } from 'primereact/card';

import { useSelector } from 'react-redux';
import APIServices from '../service/APIService';
import { API } from '../components/constants/api_url';
import * as XLSX from 'xlsx';
import ESGChart, { ESGChartConfigs, ESGColorSchemes } from '../components/charts/ESGChart';

const ESGLiveDashboard = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [filters, setFilters] = useState({
        country: null,
        businessUnit: null,
        site: null,
        year: new Date().getFullYear() // Automatically set to current year
    });

    // User access control - RBAC implementation
    const [userAccess, setUserAccess] = useState({
        type: 'internal', // 'internal' or 'external'
        permissions: {
            canView: true,
            canDownload: true,
            canShare: true
        }
    });

    // Chart interactivity state
    const [electricityChartView, setElectricityChartView] = useState('year'); // 'year' or 'month'

    // Filter options
    const [countryOptions, setCountryOptions] = useState([]);
    const [businessUnitOptions, setBusinessUnitOptions] = useState([]);
    const [siteOptions, setSiteOptions] = useState([]);
    // Dynamic year options - automatically includes current year
    const generateYearOptions = () => {
        const currentYear = new Date().getFullYear();
        const startYear = 2020;
        const options = [];

        for (let year = currentYear; year >= startYear; year--) {
            options.push({ label: year.toString(), value: year });
        }

        return options;
    };

    const [yearOptions, setYearOptions] = useState(generateYearOptions());

    // Chart data states
    const [chartData, setChartData] = useState({
        highlights: {},
        environment: {},
        social: {},
        governance: {}
    });

    // Chart refs for cleanup
    const chartRefs = useRef({});

    const selector = useSelector(state => state.user.userdetail);

    useEffect(() => {
        loadFilterOptions();
        loadChartData();
        // Initialize user access based on role
        if (selector && selector.role) {
            setUserAccess(prev => ({
                ...prev,
                type: selector.role === 'external' ? 'external' : 'internal',
                permissions: {
                    canView: true,
                    canDownload: selector.role !== 'external' || selector.permissions?.canDownload,
                    canShare: selector.role !== 'external' || selector.permissions?.canShare
                }
            }));
        }
    }, [selector]);

    useEffect(() => {
        loadChartData();
    }, [filters]);

    const loadFilterOptions = async () => {
        try {
            // Use the same API structure as ESGReport.js for Country and Business Unit filters
            const uriString = {
                include: [
                    {
                        relation: "locationTwos",
                        scope: { include: [{ relation: "locationThrees" }] },
                    },
                ],
            };

            const response = await APIServices.get(
                API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
            );

            // Process countries (locationOnes)
            const countries = [
                { label: 'Global', value: 'Global' },
                ...response.data.map(country => ({
                    label: country.name,
                    value: country.name
                }))
            ];
            setCountryOptions(countries);

            // Process business units (locationTwos) - entities from ESGReport.js
            const allBusinessUnits = [];
            response.data.forEach(country => {
                if (country.locationTwos && country.locationTwos.length > 0) {
                    country.locationTwos.forEach(locationTwo => {
                        if (locationTwo.locationThrees && locationTwo.locationThrees.length > 0) {
                            allBusinessUnits.push({
                                label: locationTwo.name,
                                value: locationTwo.name,
                                countryName: country.name
                            });
                        }
                    });
                }
            });
            setBusinessUnitOptions(allBusinessUnits);

            // Process sites (locationThrees)
            const allSites = [];
            response.data.forEach(country => {
                if (country.locationTwos && country.locationTwos.length > 0) {
                    country.locationTwos.forEach(locationTwo => {
                        if (locationTwo.locationThrees && locationTwo.locationThrees.length > 0) {
                            locationTwo.locationThrees.forEach(locationThree => {
                                allSites.push({
                                    label: locationThree.name,
                                    value: locationThree.name,
                                    businessUnit: locationTwo.name,
                                    countryName: country.name
                                });
                            });
                        }
                    });
                }
            });
            setSiteOptions(allSites);
        } catch (error) {
            console.error('Error loading filter options:', error);
        }
    };

    const loadChartData = async () => {
        try {
            // This will be implemented with actual API calls based on the specifications
            // For now, using mock data structure
            const mockData = {
                highlights: {
                    carbonIntensity: generateMockChartData('Carbon Intensity'),
                    carbonFootprint: generateMockChartData('Scope 1+2 Market Based (Carbon Footprint)', 'comparison'),
                    renewableEnergyFactor: generateMockChartData('Renewable Energy Factor (REF)', 'comparison'),
                    powerUsageEffectiveness: generateMockChartData('Power Usage Effectiveness (PUE)'),
                    greenDataCentre: generateMockChartData('Green Data Centre'),
                    waterUsageEffectiveness: generateMockChartData('Water Usage Effectiveness (WUE)'),
                    safety: generateMockChartData('Safety'),
                    diversity: generateMockChartData('Diversity'),
                    greenFinancing: generateMockChartData('Green Financing'),
                    antiCorruption: generateMockChartData('Anti-Corruption')
                },
                environment: {
                    scope1And2Emissions: generateMockChartData('Scope 1 + 2 GHG Emissions (Target vs Actual)', 'comparison'),
                    electricityConsumption: generateMockChartData('Electricity Consumption – Renewable vs Non-renewable', 'electricity'),
                    carbonIntensity: generateMockChartData('Carbon Intensity'),
                    renewablesBreakdown: generateMockChartData('Renewables Breakdown by Type (RECs vs PPA vs GREEN Tariffs)', 'pie'),
                    ref: generateMockChartData('Renewable Energy Factor (Projection vs Actual)', 'comparison'),
                    pue: generateMockChartData('PUE1 – Power Usage Effectiveness'),
                    wue: generateMockChartData('WUE1 – Water Usage Effectiveness')
                },
                social: {
                    constructionHours: generateMockChartData('Construction & Operations Hours vs TRIR'),
                    recordableIncidents: generateMockChartData('Number of Recordable Incidents'),
                    genderDiversity: generateMockChartData('Gender Diversity (Women %)'),
                    womenInLeadership: generateMockChartData('Percentage of Women in Leadership'),
                    newHiresAndTurnover: generateMockChartData('New Hires and Turnover')
                },
                governance: {
                    // Governance charts will be added when DCF forms are ready
                }
            };
            setChartData(mockData);
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    };

    // Handle electricity chart click to toggle between year and month view
    const handleElectricityChartClick = () => {
        setElectricityChartView(prev => prev === 'year' ? 'month' : 'year');
        // Reload chart data to reflect the new view
        setTimeout(() => {
            loadChartData();
        }, 100);
    };

    const generateMockChartData = (title, type = 'bar') => {
        // Dynamic year generation - automatically includes current year
        const currentYear = new Date().getFullYear();
        const startYear = 2020;
        const years = [];
        for (let year = startYear; year <= currentYear; year++) {
            years.push(year.toString());
        }

        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        if (type === 'comparison') {
            // Generate realistic data for comparison charts with stacked layout (actual on top, target below)
            const actualData = [];
            const targetData = [];

            // Generate data for each year dynamically
            for (let i = 0; i < years.length; i++) {
                if (title.includes('Scope 1')) {
                    // Decreasing emissions trend
                    actualData.push(2300 - (i * 200) + Math.random() * 100);
                    targetData.push(2200 - (i * 180) + Math.random() * 80);
                } else if (title.includes('Renewable Energy Factor')) {
                    // Increasing REF trend
                    actualData.push(45 + (i * 7) + Math.random() * 5);
                    targetData.push(50 + (i * 8) + Math.random() * 5);
                }
            }

            return {
                title,
                labels: years,
                datasets: [
                    {
                        label: 'Actual',
                        data: actualData,
                        backgroundColor: '#3B82F6', // Blue for actual
                        borderColor: '#3B82F6',
                        borderWidth: 1,
                        stack: 'Stack 0'
                    },
                    {
                        label: 'Target',
                        data: targetData,
                        backgroundColor: '#8B5CF6', // Purple for target
                        borderColor: '#8B5CF6',
                        borderWidth: 1,
                        stack: 'Stack 1'
                    }
                ]
            };
        } else if (type === 'electricity') {
            // Special handling for electricity consumption chart with dynamic year/month toggle
            const labels = electricityChartView === 'month' ? months : years;
            let renewableData, nonRenewableData;

            if (electricityChartView === 'month') {
                // Monthly data for current year
                renewableData = [450, 480, 520, 550, 580, 600, 620, 610, 590, 570, 540, 500];
                nonRenewableData = [550, 520, 480, 450, 420, 400, 380, 390, 410, 430, 460, 500];
            } else {
                // Yearly data - generate for all years dynamically
                renewableData = [];
                nonRenewableData = [];
                for (let i = 0; i < years.length; i++) {
                    renewableData.push(500 + (i * 20) + Math.random() * 50);
                    nonRenewableData.push(600 - (i * 15) + Math.random() * 40);
                }
            }

            return {
                title,
                labels,
                datasets: [
                    {
                        label: 'Renewable',
                        data: renewableData,
                        backgroundColor: '#10B981', // Green for renewable
                        borderColor: '#10B981',
                        borderWidth: 1,
                        stack: 'Stack 0'
                    },
                    {
                        label: 'Non-renewable',
                        data: nonRenewableData,
                        backgroundColor: '#EF4444', // Red for non-renewable
                        borderColor: '#EF4444',
                        borderWidth: 1,
                        stack: 'Stack 0'
                    }
                ]
            };
        } else if (type === 'pie') {
            return {
                title,
                labels: ['RECs', 'PPA', 'GREEN Tariffs'],
                datasets: [{
                    data: [45, 35, 20],
                    backgroundColor: ['#3B82F6', '#10B981', '#F59E0B'],
                    borderWidth: 1
                }]
            };
        } else if (title.includes('PUE')) {
            // PUE values should be around 1.0-2.0 - generate for all years dynamically
            const pueData = [];
            for (let i = 0; i < years.length; i++) {
                // Decreasing PUE trend (better efficiency)
                pueData.push(1.68 - (i * 0.04) + (Math.random() * 0.02 - 0.01));
            }

            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: pueData,
                    backgroundColor: '#06B6D4', // Cyan for PUE
                    borderColor: '#06B6D4',
                    borderWidth: 1
                }]
            };
        } else if (title.includes('WUE')) {
            // WUE values - generate for all years dynamically
            const wueData = [];
            for (let i = 0; i < years.length; i++) {
                // Decreasing WUE trend (better efficiency)
                wueData.push(1.11 - (i * 0.05) + (Math.random() * 0.02 - 0.01));
            }

            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: wueData,
                    backgroundColor: '#0EA5E9', // Sky blue for WUE
                    borderColor: '#0EA5E9',
                    borderWidth: 1
                }]
            };
        } else if (title.includes('REF') || title.includes('Renewable Energy Factor')) {
            // REF with projection vs actual
            return {
                title,
                labels: years,
                datasets: [
                    {
                        label: 'Projection',
                        data: [50, 60, 70, 80, 85, 90],
                        backgroundColor: '#F59E0B', // Orange for projection
                        borderColor: '#F59E0B',
                        borderWidth: 1,
                        stack: 'Stack 0'
                    },
                    {
                        label: 'Actual',
                        data: [45, 55, 65, 75, 80, 85],
                        backgroundColor: '#3B82F6', // Blue for actual
                        borderColor: '#3B82F6',
                        borderWidth: 1,
                        stack: 'Stack 1'
                    }
                ]
            };
        } else {
            // Default chart handling - generate data for all years dynamically
            const chartData = [];

            if (title.includes('Carbon Intensity')) {
                // Carbon intensity should decrease over time
                for (let i = 0; i < years.length; i++) {
                    chartData.push(0.8 - (i * 0.1) + (Math.random() * 0.05 - 0.025));
                }
            } else {
                // Generic data for other charts
                for (let i = 0; i < years.length; i++) {
                    chartData.push(Math.floor(Math.random() * 1000) + 100);
                }
            }

            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: chartData,
                    backgroundColor: title.includes('Carbon Intensity') ? '#F59E0B' : ESGColorSchemes.primary[0],
                    borderColor: title.includes('Carbon Intensity') ? '#F59E0B' : ESGColorSchemes.primary[0],
                    borderWidth: 1
                }]
            };
        }
    };

    const handleFilterChange = (filterType, value) => {
        setFilters(prev => ({
            ...prev,
            [filterType]: value
        }));
    };

    const handleShare = () => {
        // Implement share functionality
        console.log('Share dashboard');
    };

    const handleDownload = (format = 'excel') => {
        try {
            if (format === 'excel') {
                // Create workbook
                const wb = XLSX.utils.book_new();

                // Add highlights data
                const highlightsData = Object.entries(chartData.highlights).map(([key, data]) => ({
                    Chart: data.title,
                    '2020': data.datasets[0].data[0],
                    '2021': data.datasets[0].data[1],
                    '2022': data.datasets[0].data[2],
                    '2023': data.datasets[0].data[3],
                    '2024': data.datasets[0].data[4]
                }));

                const highlightsWS = XLSX.utils.json_to_sheet(highlightsData);
                XLSX.utils.book_append_sheet(wb, highlightsWS, 'Highlights');

                // Add environment data
                const environmentData = Object.entries(chartData.environment).map(([key, data]) => ({
                    Chart: data.title,
                    '2020': data.datasets[0].data[0],
                    '2021': data.datasets[0].data[1],
                    '2022': data.datasets[0].data[2],
                    '2023': data.datasets[0].data[3],
                    '2024': data.datasets[0].data[4]
                }));

                const environmentWS = XLSX.utils.json_to_sheet(environmentData);
                XLSX.utils.book_append_sheet(wb, environmentWS, 'Environment');

                // Download file
                XLSX.writeFile(wb, `ESG_Dashboard_${new Date().toISOString().split('T')[0]}.xlsx`);
            }
        } catch (error) {
            console.error('Error downloading data:', error);
        }
    };

    const downloadMenuRef = useRef(null);
    const downloadMenuItems = [
        {
            label: 'Excel',
            icon: 'pi pi-file-excel',
            command: () => handleDownload('excel')
        }
    ];

    const renderFilters = (showAllFilters = true) => (
        <div className="flex align-items-center gap-3 mb-4">
            {showAllFilters && (
                <>
                    <div className="flex align-items-center gap-2">
                        <label className="text-bold fs-12">Country:</label>
                        <Dropdown
                            value={filters.country}
                            options={countryOptions}
                            onChange={(e) => handleFilterChange('country', e.value)}
                            placeholder="United States"
                            className="filter-dropdown"
                            style={{ minWidth: '150px' }}
                        />
                    </div>
                    <div className="flex align-items-center gap-2">
                        <label className="text-bold fs-12">Business Unit:</label>
                        <Dropdown
                            value={filters.businessUnit}
                            options={businessUnitOptions}
                            onChange={(e) => handleFilterChange('businessUnit', e.value)}
                            placeholder="Manufacturing"
                            className="filter-dropdown"
                            style={{ minWidth: '150px' }}
                        />
                    </div>
                    <div className="flex align-items-center gap-2">
                        <label className="text-bold fs-12">Site:</label>
                        <Dropdown
                            value={filters.site}
                            options={siteOptions}
                            onChange={(e) => handleFilterChange('site', e.value)}
                            placeholder="Manufacturing Plant 1"
                            className="filter-dropdown"
                            style={{ minWidth: '180px' }}
                        />
                    </div>
                </>
            )}
            <div className="flex align-items-center gap-2">
                <label className="text-bold fs-12">Year:</label>
                <Dropdown
                    value={filters.year}
                    options={yearOptions}
                    onChange={(e) => handleFilterChange('year', e.value)}
                    placeholder="2024"
                    className="filter-dropdown"
                    style={{ minWidth: '100px' }}
                />
            </div>
            <div className="flex gap-2 ml-auto">
                {userAccess.permissions.canShare && (
                    <Button
                        label="Share"
                        icon="pi pi-share-alt"
                        className="p-button-outlined"
                        onClick={handleShare}
                    />
                )}
                {userAccess.permissions.canDownload && (
                    <Button
                        label="Download"
                        icon="pi pi-download"
                        onClick={() => handleDownload('excel')}
                    />
                )}
                <Menu model={downloadMenuItems} popup ref={downloadMenuRef} />
            </div>
        </div>
    );

    return (
        <div className="bg-smoke font-lato" style={{ padding: '20px', paddingTop: '90px' }}>
            <div className="col-12">
                {/* Header */}
                <div className="mb-4">
                    <div className="flex align-items-center justify-content-between">
                        <div>
                            <h2 className="text-big-one clr-navy fs-24 mb-2">ESG Live Dashboard</h2>
                            <p className="text-micro clr-navy fs-14">
                                Comprehensive view of Environmental, Social, and Governance metrics
                            </p>
                        </div>
                        <div className="flex align-items-center gap-3">
                            {/* User Type Selector */}
                            {/* <div className="flex align-items-center gap-2">
                                <label className="text-bold fs-12 clr-navy">Access Type:</label>
                                <Dropdown
                                    value={userAccess.type}
                                    options={[
                                        { label: 'Internal User', value: 'internal' },
                                        { label: 'External User', value: 'external' }
                                    ]}
                                    onChange={(e) => setUserAccess(prev => ({
                                        ...prev,
                                        type: e.value,
                                        permissions: {
                                            canView: true,
                                            canDownload: e.value === 'internal',
                                            canShare: e.value === 'internal'
                                        }
                                    }))}
                                    className="filter-dropdown"
                                    style={{ minWidth: '140px' }}
                                />
                            </div> */}

                            {/* User Info */}
                            <div className="flex align-items-center gap-2">
                                <span className="text-bold fs-12 clr-navy">
                                    {selector?.name || 'John Doe'} - Sustainability Manager
                                </span>
                                <div className="user-avatar bg-primary text-white border-circle flex align-items-center justify-content-center"
                                     style={{ width: '32px', height: '32px', fontSize: '12px' }}>
                                    {(selector?.name || 'John Doe').split(' ').map(n => n[0]).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters - Show all filters except for Highlights section */}
                {activeIndex !== 0 && renderFilters()}

                {/* Tab Navigation */}
                <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                    <TabPanel header="Highlights">
                        <div className="grid">
                            {/* Highlights section only has year filter as specified */}
                            <div className="col-12 mb-3">
                                <div className="flex align-items-center gap-2">
                                    <label className="text-bold fs-12">Year:</label>
                                    <Dropdown
                                        value={filters.year}
                                        options={yearOptions}
                                        onChange={(e) => handleFilterChange('year', e.value)}
                                        placeholder="2024"
                                        className="filter-dropdown"
                                        style={{ minWidth: '100px' }}
                                    />
                                </div>
                            </div>

                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="carbon-intensity-highlights"
                                        type="bar"
                                        data={chartData.highlights.carbonIntensity}
                                        {...ESGChartConfigs.carbonIntensity}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="carbon-footprint-highlights"
                                        type="bar"
                                        data={chartData.highlights.carbonFootprint}
                                        {...ESGChartConfigs.carbonFootprint}
                                        height={300}
                                    />
                                </Card>
                            </div>

               
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="ref-highlights"
                                        type="bar"
                                        data={chartData.highlights.renewableEnergyFactor}
                                        {...ESGChartConfigs.renewableEnergyFactor}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="pue-highlights"
                                        type="bar"
                                        data={chartData.highlights.powerUsageEffectiveness}
                                        {...ESGChartConfigs.powerUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="wue-highlights"
                                        type="bar"
                                        data={chartData.highlights.waterUsageEffectiveness}
                                        {...ESGChartConfigs.waterUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="green-data-centre-highlights"
                                        type="bar"
                                        data={chartData.highlights.greenDataCentre}
                                        title="Green Data Centre"
                                        height={300}
                                    />
                                </Card>
                            </div>

                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="safety-highlights"
                                        type="bar"
                                        data={chartData.highlights.safety}
                                        title="Safety"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="diversity-highlights"
                                        type="bar"
                                        data={chartData.highlights.diversity}
                                        title="Diversity"
                                        height={300}
                                    />
                                </Card>
                            </div>

                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="green-financing-highlights"
                                        type="bar"
                                        data={chartData.highlights.greenFinancing}
                                        title="Green Financing"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="anti-corruption-highlights"
                                        type="bar"
                                        data={chartData.highlights.antiCorruption}
                                        title="Anti-Corruption"
                                        height={300}
                                    />
                                </Card>
                            </div>
                        </div>
                    </TabPanel>
                    
                    <TabPanel header="Environment">
                        <div className="grid">
                           
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            Scope 1 + 2 GHG Emissions (Target vs Actual)
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="scope1-2-emissions-env"
                                        type="bar"
                                        data={chartData.environment.scope1And2Emissions}
                                        {...ESGChartConfigs.scope1And2Emissions}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            Electricity Consumption – Renewable vs Non-renewable
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="electricity-consumption-env"
                                        type="bar"
                                        data={chartData.environment.electricityConsumption}
                                        {...ESGChartConfigs.electricityConsumption}
                                        height={300}
                                        onClick={handleElectricityChartClick}
                                        style={{ cursor: 'pointer' }}
                                    />
                                </Card>
                            </div>

                            {/* Row 2: Carbon Intensity & Renewables Breakdown */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            Carbon Intensity
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="carbon-intensity-env"
                                        type="bar"
                                        data={chartData.environment.carbonIntensity}
                                        {...ESGChartConfigs.carbonIntensity}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            Renewables Breakdown by Type (RECs vs PPA vs GREEN Tariffs)
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="renewables-breakdown-env"
                                        type="pie"
                                        data={chartData.environment.renewablesBreakdown}
                                        {...ESGChartConfigs.renewablesBreakdown}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 3: REF & PUE */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            Renewable Energy Factor (Projection vs Actual)
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="ref-env"
                                        type="bar"
                                        data={chartData.environment.ref}
                                        {...ESGChartConfigs.renewableEnergyFactor}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            PUE1 – Power Usage Effectiveness
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="pue-env"
                                        type="bar"
                                        data={chartData.environment.pue}
                                        {...ESGChartConfigs.powerUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 4: WUE */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <div className="card-header bg-white border-bottom-0 pb-0">
                                        <h6 className="card-title text-center fw-bold mb-3">
                                            WUE1 – Water Usage Effectiveness
                                        </h6>
                                    </div>
                                    <ESGChart
                                        id="wue-env"
                                        type="bar"
                                        data={chartData.environment.wue}
                                        {...ESGChartConfigs.waterUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>

                        </div>
                    </TabPanel>
                    
                    <TabPanel header="Social">
                        <div className="grid">
                            {/* Note: Social section is currently paused as DCF forms are not ready */}
                            <div className="col-12 mb-4">
                                <div className="card bg-yellow-50 border-yellow-200 p-4">
                                    <div className="flex align-items-center gap-3">
                                        <i className="pi pi-info-circle text-yellow-600 fs-20"></i>
                                        <div>
                                            <h5 className="text-bold fs-16 text-yellow-800 mb-2">Social Section - Development Paused</h5>
                                            <p className="fs-14 text-yellow-700 mb-0">
                                                The Social section charts are currently paused as the required DCF forms are not yet ready.
                                                This section will include metrics such as Construction & Operations Hours vs TRIR,
                                                Number of Recordable Incidents, Gender Diversity, Women in Leadership, and New Hires and Turnover.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Placeholder charts for Social section (commented out until DCF forms are ready) */}
                            {/*
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="construction-hours-social"
                                        type="bar"
                                        data={chartData.social.constructionHours}
                                        title="Construction & Operations Hours vs TRIR"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="recordable-incidents-social"
                                        type="bar"
                                        data={chartData.social.recordableIncidents}
                                        title="Number of Recordable Incidents"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="gender-diversity-social"
                                        type="bar"
                                        data={chartData.social.genderDiversity}
                                        title="Gender Diversity (Women %)"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="women-leadership-social"
                                        type="bar"
                                        data={chartData.social.womenInLeadership}
                                        title="Percentage of Women in Leadership"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-12">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="new-hires-turnover-social"
                                        type="bar"
                                        data={chartData.social.newHiresAndTurnover}
                                        title="New Hires and Turnover"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            */}
                        </div>
                    </TabPanel>
                    
                    <TabPanel header="Governance">
                        <div className="grid">
                            {/* Note: Governance section is currently paused as DCF forms are not ready */}
                            <div className="col-12 mb-4">
                                <div className="card bg-yellow-50 border-yellow-200 p-4">
                                    <div className="flex align-items-center gap-3">
                                        <i className="pi pi-info-circle text-yellow-600 fs-20"></i>
                                        <div>
                                            <h5 className="text-bold fs-16 text-yellow-800 mb-2">Governance Section - Development Paused</h5>
                                            <p className="fs-14 text-yellow-700 mb-0">
                                                The Governance section charts are currently paused as the required DCF forms are not yet ready.
                                                This section will include governance-related metrics such as board composition,
                                                anti-corruption practices, and other governance indicators.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                           
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>
    );
};

export default ESGLiveDashboard;
